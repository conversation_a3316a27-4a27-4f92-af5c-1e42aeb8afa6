stages:
  - build
  - test

variables:
  PROJECT_NAME: "ADK Agent"

# Environment check
check-environment:
  stage: build
  script:
    - echo "🔍 Environment Check for ${PROJECT_NAME}"
    - echo "User: $(whoami)"
    - echo "Directory: $(pwd)"
    - echo "Shell: $SHELL"
    - echo "Date: $(date)"
    - echo "✅ Environment OK"
  tags:
    - test

# Build project
build-project:
  stage: build
  script:
    - echo "Building the project..."
  tags:
    - test
